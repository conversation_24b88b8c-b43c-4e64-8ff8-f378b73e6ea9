@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-made-tommy-soft);
  --font-mono: var(--font-made-tommy-soft);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  /* Custom theme colors */

  --color-brand-100: #202325CC;
  --color-brand-200: #20232599;
  --color-brand-300: #20232566;
  --color-brand-400: #F2F2F2;
  --color-brand-500: #F6B65B;
  --color-brand-600: #202325;
  --color-brand-700: #D8D8D8;
  --color-brand-800: #C0405A;
  --color-brand-900: #00000080;
  --color-brand-1000: #6DBFFA;
  --color-brand-1100: #F5F5F5;
  --color-brand-1200: #BCBCBC;
  --color-brand-1300: #F5F5F5A8;
  --color-brand-1400: #2023250D;
  --color-brand-1500: #D9D9D9;
  --color-brand-1600: #656A6A;
  --color-brand-1700: #D4D4D4;
  --color-brand-green: #74C242;
  --color-brand-gray-50: #888888;
  --color-brand-gray-100: #262E3473;
  --color-brand-gray-150: #20232580;
  --color-brand-gray-200: #C7C7C7;
  --color-brand-gray-250: #E9E9E9;
  --color-brand-gray-300: #BBBBBB;
  --color-brand-gray-350: #EDEDED;
  --color-brand-gray-400: #646464;
  --color-brand-gray-450: #DBDBDB;
  --color-brand-gray-500: #CECECE80;
  --color-brand-gray-550: #A2A2A2;
  --color-brand-gray-600: #EBEBEB;
  --color-brand-gray-650: #F4F4F4A1;
  --color-brand-gray-660: #F4F4F4;
  --color-brand-gray-700: #F3F3F3;
  --color-brand-gray-750: #F8F8F8;
  --color-brand-gray-800: #F0EFEF;
  --color-brand-gray-850: #E0E0E0;
  --color-brand-gray-900: #2023251A;
  --color-brand-gray-950: #4D4D4D;
  --color-brand-gray-960: #C6C6C6;
  --color-brand-gray-970: #9A9A9A;
  --color-brand-gray-980: #E1E1E1;
  --color-brand-gray-990: #9A9A9A47;
  --color-brand-gray-995: #B6B6B6;
  --color-brand-gray-1000: #e5e5e3;
  --color-brand-gray-1001: #D3D3D3;
  --color-brand-gray-1002: #6E6E6E;
  --color-brand-gray-1003: #AEAEAE;
  --color-brand-gray-1004: #696969;
  --color-brand-gray-1005: #707070;
  --color-brand-gray-1006: #CFCFCF;
  --color-brand-gray-1007: #E3E3E3;
  --color-brand-gray-1008: #F9F9F9;
  --color-brand-gray-1009: #848484;  
  --color-brand-gray-1010: #828282;
  --color-brand-blue-50: #6DBFFA;
  --color-brand-blue-100: #649ff9;
  --color-brand-blue-200: #23A0FA;
  --color-brand-blue-300: #0040BB;
  --color-brand-blue-400: #5EB4F2;
  --color-brand-black-50: #2E2E2E;
  --color-brand-black-100: #202325D9;
  --color-brand-black-150: #00000017;
  --color-brand-black-200: #2B2B2B;
  --color-brand-black-300: #3E3E3E;
  --color-brand-red-500: #E34343;
  --color-brand-red-600: #DA7070;
  --color-brand-red-700: #EC0000;
  --color-brand-facebook: #3383FB;

  /* Custom theme shadows */
  --shadow-custom: 0px 2px 8px -6px rgba(0, 0, 0, 0.08);
  --shadow-custom-2: 0px 0px 46.3px var(--color-brand-blue-50);
  --shadow-custom-3: 0px 19px 35.3px -11px var(--color-brand-blue-50);
  --shadow-custom-4: 0px 0px 25px 0px #00000040;
  --shadow-custom-5: 0px 2px 9px 0px #0000001A;
  --shadow-custom-6: 0px 2px 9px 0px #00000033;
  --shadow-custom-7: 0px 0px 25px 0px #00000026;
  --shadow-location-dropdown: 0px 0px 25px 0px #00000012;
  --shadow-customHover: 0px 8px 20.8px -5px (--color-brand-blue-50);
  --shadow-customBox: 0px 4px 30px 0px #0000001A;
  --shadow-customBorder: 0px 2px 4.6px 0px #0000002E;
  --shadow-customBox2: 0px 7px 10px 0px #00000021;
  --shadow-customBox3: 0px 4px 20px 0px #00000021;
  --gradient-custom: linear-gradient(180deg, var(--color-brand-blue-50) 0%, var(--color-brand-600) 100%)linear-gradient(90deg, var(--color-brand-blue-50) 0%, var(--color-brand-blue-50) 100%);
  --border-line: linear-gradient(90deg, #F5F5F5 0%, var(--color-brand-gray-850) 40.5%, var(--color-brand-gray-850) 100%);
  --background-custom2: linear-gradient(90deg, var(--color-brand-blue-50) 0%, var(--color-brand-blue-50) 100%);;
  --background-custom3: linear-gradient(90deg, rgba(35, 160, 250, 0.65) 0%, rgba(108, 191, 251, 0.65) 100%);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);

}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
  
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground font-normal;
    font-family: var(--font-made-tommy-soft), system-ui, arial;
    font-weight: 400;
  }
  
  /* Ensure Made Tommy Soft is used everywhere with default weight 400 */
  * {
    font-family: inherit;
  }
}

.filters-modal-content button.ring-offset-background,
.signup-modal-content button.ring-offset-background {
  display: none;
}

.no-scroll {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
}

.no-scroll::-webkit-scrollbar {
  display: none; /* Chrome/Safari/Opera */
}

.search-filter:after {
  background-color: var(--color-brand-gray-800);
  content: "";
  z-index: 2;
  width: 2px;
  height: 44px;
  position: absolute;
  top: 8px;
  right: -24px;
}

.search-filter:before {
  content: "";
  z-index: 2;
  background-color: #000;
  width: 62%;
  height: 4px;
  position: absolute;
  bottom: -13px;
}

.secondary-button {
  background: linear-gradient(90deg, var(--color-brand-blue-50) 0%, var(--color-brand-blue-50) 100%);
  box-shadow: 0px 19px 35.3px -11px var(--color-brand-blue-50);
}
.bg-background-custom3 {
background: linear-gradient(90deg, var(--color-brand-blue-50) 0%, var(--color-brand-blue-50) 100%);
}


.gradient-text {
  background: linear-gradient(95.14deg, #F26E20 3.3%, #FD1E31 20.41%, #904291 61.35%, #5155EA 106.41%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
  display: inline-block; /* This might be needed for proper rendering */
  font-weight: 500;
}

.bg-background-disabled {
  background: linear-gradient(90deg, rgba(202, 202, 202, 0.65) 0%, rgba(135, 135, 135, 0.65) 100%);
}

.gradient-border-wrapper {
  padding: 2px;
  background: linear-gradient(117.09deg, #B8E1FF 5.04%, #5FB5F2 112.74%);
  transition: background 0.3s ease;
}

.gradient-border-wrapper:focus-within {
  background: linear-gradient(117.09deg, #B8F2FF 5.04%, #5F65F2 112.74%);
}




@media (min-width: 1200px) {
  .container  {
    max-width: 1200px;
  }
  .container.inner-page-container {
    max-width: 1330px;
  }
}