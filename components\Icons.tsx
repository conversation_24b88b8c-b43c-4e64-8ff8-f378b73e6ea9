export const SearchIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="60"
            height="57"
            fill="none"
            viewBox="0 0 60 57"
        >
            <path
                stroke="#6DBFFA"
                strokeLinecap="round"
                strokeMiterlimit="10"
                strokeWidth="4"
                d="M16.994 14.063c-4.204 4.233-4.204 11.1 0 15.333M37.942 31.576l15.316 15.42a2.314 2.314 0 0 1 0 3.253 2.276 2.276 0 0 1-3.232 0L34.754 34.873a16.6 16.6 0 0 0 3.185-3.297z"
            ></path>
            <path
                stroke="#6DBFFA"
                strokeMiterlimit="10"
                strokeWidth="4"
                d="M24.609 5.081c9.132 0 16.536 7.454 16.536 16.649a16.66 16.66 0 0 1-6.388 13.143 16.4 16.4 0 0 1-10.144 3.505c-9.132 0-16.536-7.454-16.536-16.648 0-9.195 7.4-16.649 16.532-16.649Z"
            ></path>
        </svg>
    )
}

export const RequestIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="49"
            height="54"
            fill="none"
            viewBox="0 0 49 54"
        >
            <path
                stroke="#6DBFFA"
                strokeLinecap="round"
                strokeMiterlimit="10"
                strokeWidth="4"
                d="M35 10.26V5.703C35 3.658 33.355 2 31.326 2H5.674C3.644 2 2 3.658 2 5.703v42.594C2 50.342 3.645 52 5.674 52h25.652C33.356 52 35 50.342 35 48.297v-5.735M12 45h13"
            ></path>
            <path
                stroke="#6DBFFA"
                strokeMiterlimit="10"
                strokeWidth="4"
                d="M21.319 7h-5.642c-.741 0-1.42-.425-1.75-1.095L12 2h13l-1.928 3.905A1.96 1.96 0 0 1 21.323 7z"
            ></path>
            <path
                stroke="#6DBFFA"
                strokeLinecap="round"
                strokeMiterlimit="10"
                strokeWidth="4"
                d="M47 26c0 6.076-4.924 11-11 11s-11-4.924-11-11 4.924-11 11-11 11 4.924 11 11Z"
            ></path>
            <path
                stroke="#6DBFFA"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="4"
                d="m41 22-6.275 8L30 25.791"
            ></path>
        </svg>
    )
}

export const EnjoyIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="66"
            height="42"
            fill="none"
            viewBox="0 0 66 42"
        >
            <path
                stroke="#6DBFFA"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="4"
                d="M12.486 19.384c1.753 6.508 5.528 11.9 10.419 15.075 2.698-2.256 6.092-3.526 10.19-3.526s7.49 1.27 10.19 3.526c4.935-3.203 8.732-8.663 10.467-15.253-6.128 2.02-13.27 3.18-20.9 3.18s-14.358-1.092-20.361-3.007z"
            ></path>
            <path
                stroke="#6DBFFA"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="4"
                d="M22.904 34.464c3.04 1.973 6.505 3.093 10.19 3.093s7.15-1.12 10.19-3.093c-2.699-2.256-6.093-3.525-10.19-3.525s-7.491 1.27-10.19 3.525M53.666 11.13c-6.11 2.006-13.224 3.155-20.818 3.155s-14.324-1.087-20.322-2.992l-1.107.914a31.6 31.6 0 0 0 1.062 7.177C18.49 21.3 25.437 22.39 32.843 22.39s14.771-1.159 20.9-3.179c.596-2.27.95-4.675 1.022-7.167l-1.103-.914zM9.42 3.443S4.344 4.483 4.344 9.22M56.58 3.443s5.075 1.04 5.075 5.777"
            ></path>
        </svg>
    )
}

export const UploadIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="48"
            height="48"
            fill="none"
            viewBox="0 0 48 48"
        >
            <path
                stroke="#6DBFFA"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="4"
                d="M42 30v8a4 4 0 0 1-4 4H10a4 4 0 0 1-4-4v-8m28-14L24 6m0 0L14 16M24 6v24"
            ></path>
        </svg>
    )
}

export const PaidIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="68"
            height="66"
            fill="none"
            viewBox="0 0 68 66"
        >
            <path
                stroke="#6DBFFA"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2.5"
                d="M44.829 40.21c6.618 0 11.984-5.352 11.984-11.956s-5.366-11.956-11.984-11.956-11.984 5.353-11.984 11.956c0 6.604 5.365 11.957 11.984 11.957"
            ></path>
            <path
                stroke="#6DBFFA"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="3"
                d="M35.985 7.267v6.102M53.51 7.267v6.102M44.748 4.205v6.103"
            ></path>
            <path
                stroke="#6DBFFA"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M45 22v2M45 33v2M42 31.194v.38c0 .787.592 1.426 1.323 1.426h3.354c.73 0 1.323-.639 1.323-1.427v-1.61c0-.789-.592-1.427-1.323-1.427h-3.354c-.73 0-1.323-.639-1.323-1.427v-1.683c0-.787.592-1.426 1.323-1.426h3.354c.73 0 1.323.639 1.323 1.427v.651"
            ></path>
            <path
                stroke="#6DBFFA"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="4"
                d="m5.208 45.395 5.887-2.15 2.203 6.119M13.817 62.795l3.826-1.374-2.883-8.007M12.295 45.655l11.25-4.407a6.8 6.8 0 0 1 5.481.229l12.576 6.179a2.85 2.85 0 0 1 1.293 3.837 2.865 2.865 0 0 1-3.685 1.346l-6.704-2.869"
            ></path>
            <path
                stroke="#6DBFFA"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="4"
                d="m17.019 57.962 5.21-2.347a3.96 3.96 0 0 1 3-.1l11.513 4.282c.898.334 1.893.31 2.775-.068l21.463-9.24a2.99 2.99 0 0 0 1.78-3.158 2.998 2.998 0 0 0-3.766-2.471l-14.818 4.07"
            ></path>
        </svg>
    )
}

export const AlertTriangleIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="70"
            height="71"
            fill="none"
            viewBox="0 0 70 71"
        >
            <path
                fill="#E9E9E9"
                d="M30.012 11.63 5.308 52.872a5.834 5.834 0 0 0 4.988 8.75h49.408a5.833 5.833 0 0 0 4.987-8.75L39.987 11.63a5.834 5.834 0 0 0-9.975 0"
            ></path>
            <path
                stroke="#fff"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="5"
                d="M35 26.622v11.667m0 11.666h.029M30.012 11.63 5.308 52.872a5.834 5.834 0 0 0 4.988 8.75h49.408a5.833 5.833 0 0 0 4.987-8.75L39.987 11.63a5.834 5.834 0 0 0-9.975 0"
            ></path>
        </svg>
    )


}

export const CalendarIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="26"
            height="26"
            fill="none"
            viewBox="0 0 26 26"
        >
            <path
                stroke="#222"
                strokeWidth="1.6"
                d="M15.632 6.616c0-.943 0-1.414.293-1.707s.764-.293 1.707-.293h3.291c.943 0 1.415 0 1.708.293s.293.764.293 1.707v13.708c0 .943 0 1.414-.293 1.707s-.765.293-1.707.293h-3.292c-.943 0-1.414 0-1.707-.293s-.293-.764-.293-1.707z"
            ></path>
            <path
                stroke="#222"
                strokeWidth="1.6"
                d="M17.715 18.657c0-.235 0-.353.073-.427.073-.073.191-.073.427-.073h2.125c.236 0 .353 0 .427.073.073.074.073.192.073.427v1.084c0 .235 0 .353-.073.426-.074.074-.191.074-.427.074h-2.125c-.236 0-.354 0-.427-.074-.073-.073-.073-.19-.073-.426z"
            ></path>
            <path
                stroke="#222"
                strokeLinecap="round"
                strokeWidth="1.6"
                d="M17.715 7.74h3.125M17.715 10.866h3.125M15.632 16.074h7.292M19.799 4.616h-8.417c-.943 0-1.414 0-1.707.293s-.293.764-.293 1.707v13.708c0 .943 0 1.414.293 1.707s.764.293 1.707.293h8.417"
            ></path>
            <path
                stroke="#222"
                strokeWidth="1.6"
                d="M14.069 18.157h-2.104c-.236 0-.354 0-.427.073-.073.074-.073.192-.073.427v1.084c0 .235 0 .353.073.426s.191.074.427.074h2.104"
            ></path>
            <path
                fill="#222"
                d="M11.465 6.94a.8.8 0 0 0 0 1.6zm0 1.6h2.604v-1.6h-2.604zM11.465 10.066a.8.8 0 0 0 0 1.6zm0 1.6h2.604v-1.6h-2.604z"
            ></path>
            <path stroke="#222" strokeWidth="1.6" d="M9.382 16.074h4.687"></path>
            <path
                stroke="#222"
                strokeLinecap="round"
                strokeWidth="1.6"
                d="M13.549 4.616H5.132c-.943 0-1.414 0-1.707.293s-.293.764-.293 1.707v13.708c0 .943 0 1.414.293 1.707s.764.293 1.707.293h8.417"
            ></path>
            <path
                stroke="#222"
                strokeWidth="1.6"
                d="M7.819 18.157H5.715c-.236 0-.354 0-.427.073-.073.074-.073.192-.073.427v1.084c0 .235 0 .353.073.426s.191.074.427.074h2.104"
            ></path>
            <path
                fill="#222"
                d="M5.215 6.94a.8.8 0 0 0 0 1.6zm0 1.6h2.604v-1.6H5.215zM5.215 10.066a.8.8 0 0 0 0 1.6zm0 1.6h2.604v-1.6H5.215z"
            ></path>
            <path stroke="#222" strokeWidth="1.6" d="M3.132 16.074h4.687"></path>
        </svg>
    )
}

export const GreenerEconomyIcon = () => {
    return (<svg
        xmlns="http://www.w3.org/2000/svg"
        width="382"
        height="10"
        fill="none"
        viewBox="0 0 382 10"
    >
        <path
            stroke="url(#paint0_linear_3837_16056)"
            strokeLinecap="round"
            strokeWidth="6"
            d="M3 7c95.411 0 190.308-4 285.828-4H379"
        ></path>
        <defs>
            <linearGradient
                id="paint0_linear_3837_16056"
                x1="-180.24"
                x2="948.886"
                y1="7"
                y2="6.392"
                gradientUnits="userSpaceOnUse"
            >
                <stop offset="0.056" stopColor="#4E9521" stopOpacity="0.4"></stop>
                <stop offset="0.394" stopColor="#74C242"></stop>
                <stop offset="0.637" stopColor="#74C242" stopOpacity="0.54"></stop>
            </linearGradient>
        </defs>
    </svg>)
}

export const TikTokIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="24"
            fill="none"
            viewBox="0 0 20 24"
        >
            <g clipPath="url(#clip0_3745_68580)">
                <path
                    fill="#202325"
                    fillOpacity="0.8"
                    d="M6.29 23.273c-.912-.248-1.81-.57-2.67-.961a7.32 7.32 0 0 1-3.463-7.815 7.23 7.23 0 0 1 6.013-5.72A8 8 0 0 1 7.316 8.7c.302.015.611.053.913.105v4.212a9.7 9.7 0 0 0-1.81.015c-1.607.322-2.52 1.719-2.437 3.618a3.28 3.28 0 0 0 2.761 2.86 3.26 3.26 0 0 0 3.463-2.222c.09-.383.128-.78.105-1.17q.032-7.725.053-15.45c0-.226.03-.444.053-.669h3.62c.265 3.581 2.317 5.345 5.606 5.803v3.859a9.78 9.78 0 0 1-5.447-1.802v.683c-.007 2.515.015 5.03-.037 7.545a6.99 6.99 0 0 1-4.029 6.456Q9 23.008 7.8 23.28H6.29z"
                ></path>
            </g>
            <defs>
                <clipPath id="clip0_3745_68580">
                    <path fill="#fff" d="M0 0h19.636v23.273H0z"></path>
                </clipPath>
            </defs>
        </svg>
    )
}

export const InstagramIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            fill="none"
            viewBox="0 0 24 24"
        >
            <g fill="#202325" fillOpacity="0.8" clipPath="url(#clip0_3745_68576)">
                <path d="M23.15 5.668C22.904 2.29 20.524.225 17.1.098 13.473-.038 9.84-.038 6.214.113 2.7.255.366 2.665.148 6.179.035 7.958.133 9.752.133 11.539c0 1.832-.09 3.678.015 5.503.195 3.385 2.23 5.795 5.608 6.028 3.949.263 7.905.263 11.854 0 3.288-.218 5.345-2.275 5.563-5.563a86.5 86.5 0 0 0-.022-11.839m-2.049 11.133c-.15 2.606-1.614 4.13-4.212 4.212-3.483.105-6.974.105-10.457-.008-2.373-.075-3.904-1.396-4.024-3.738-.188-3.754-.18-7.522 0-11.276A3.505 3.505 0 0 1 5.77 2.357h.27c3.761-.195 7.53-.195 11.291-.007 2.418.127 3.649 1.681 3.776 4.144.09 1.734.015 3.483.015 5.225 0 1.697.083 3.393-.022 5.082"></path>
                <path d="M11.613 5.72a5.93 5.93 0 0 0-5.87 5.999 5.93 5.93 0 0 0 5.998 5.87 5.936 5.936 0 0 0-.083-11.869h-.037m.007 9.692a3.804 3.804 0 0 1-3.723-3.828 3.865 3.865 0 0 1 3.828-3.754 3.81 3.81 0 0 1 3.77 3.844v.09a3.73 3.73 0 0 1-3.815 3.648h-.052M19.266 5.375c0 .743-.6 1.344-1.344 1.344-.743 0-1.344-.6-1.344-1.344 0-.743.6-1.344 1.344-1.344.743 0 1.344.6 1.344 1.344"></path>
            </g>
            <defs>
                <clipPath id="clip0_3745_68576">
                    <path fill="#fff" d="M.094 0h23.273v23.273H.094z"></path>
                </clipPath>
            </defs>
        </svg>
    )
}

export const FacebookIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="11"
            height="24"
            fill="none"
            viewBox="0 0 11 24"
        >
            <g clipPath="url(#clip0_3745_68582)">
                <path
                    fill="#202325"
                    fillOpacity="0.8"
                    fillRule="evenodd"
                    d="M10.681 11.59c.11-1.35.212-2.599.321-3.98H7.595c0-.791.024-1.466 0-2.141-.047-.993.384-1.404 1.308-1.365.674.03 1.347 0 2.037 0V.046a35 35 0 0 0-4.214.032c-2.514.31-3.627 1.714-3.674 4.375-.015 1.016 0 2.032 0 3.211H.82v3.926l2.24.17v11.505h4.59V11.59z"
                    clipRule="evenodd"
                ></path>
            </g>
            <defs>
                <clipPath id="clip0_3745_68582">
                    <path fill="#fff" d="M.82 0h10.182v23.273H.82z"></path>
                </clipPath>
            </defs>
        </svg>
    )
}

export const XIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="27"
            height="24"
            fill="none"
            viewBox="0 0 27 24"
        >
            <path
                fill="#202325"
                fillOpacity="0.8"
                d="m18.075 23.135.658.862h8.266l-2.66-3.492-7.73-10.146L25.332 0h-3.942l-6.68 7.863-5.33-7L8.72 0H.453l2.66 3.492 7.238 9.501L1.005 24h4.124l7.14-8.484 5.805 7.621zM11.8 11.29 4.86 2.177h2.772l5.631 7.393.723.95 8.608 11.3h-2.771l-7.001-9.189-1.024-1.341z"
            ></path>
        </svg>
    )
}

export const YoutubeIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="32"
            height="32"
            fill="none"
            viewBox="0 0 32 32"
        >
            <path
                stroke="#202325"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeOpacity="0.8"
                strokeWidth="2"
                d="M30.054 8.56a3.71 3.71 0 0 0-2.587-2.667C25.174 5.333 16 5.333 16 5.333s-9.173 0-11.466.614a3.71 3.71 0 0 0-2.587 2.666 38.7 38.7 0 0 0-.613 7.054c-.015 2.382.19 4.761.613 7.106a3.71 3.71 0 0 0 2.587 2.56c2.293.614 11.466.614 11.466.614s9.174 0 11.467-.614a3.7 3.7 0 0 0 2.587-2.666c.416-2.31.622-4.653.613-7a38.7 38.7 0 0 0-.613-7.107"
            ></path>
            <path
                stroke="#202325"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeOpacity="0.8"
                strokeWidth="2"
                d="m13 20.027 7.667-4.36L13 11.307z"
            ></path>
        </svg>
    )
}

export const ShieldCheckIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="37"
            height="37"
            fill="none"
            viewBox="0 0 37 37"
        >
            <path
                stroke="#6DBFFA"
                strokeLinecap="round"
                strokeWidth="3"
                d="M17.754 4.869a2.5 2.5 0 0 1 1.491 0l.24.088 9.705 4.16a2.5 2.5 0 0 1 1.496 2.607l-1.154 9.238a7.5 7.5 0 0 1-2.641 4.83l-6.79 5.66a2.5 2.5 0 0 1-3.202 0l-6.79-5.66a7.5 7.5 0 0 1-2.641-4.83l-1.155-9.238a2.5 2.5 0 0 1 1.496-2.607l9.706-4.16z"
            ></path>
            <path
                stroke="#6DBFFA"
                strokeLinecap="round"
                strokeWidth="3"
                d="m13.875 18.5 4.194 4.194a.5.5 0 0 0 .77-.076l5.828-8.743"
            ></path>
        </svg>
    )
}

export const UserIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="41"
            height="40"
            fill="none"
            viewBox="0 0 41 40"
        >
            <g clipPath="url(#clip0_3346_49931)">
                <rect width="40" height="40" x="0.32" fill="#fff" rx="20"></rect>
                <circle
                    cx="19.82"
                    cy="36.5"
                    r="13.5"
                    fill="#202325"
                    fillOpacity="0.2"
                ></circle>
                <circle
                    cx="20.32"
                    cy="13"
                    r="7"
                    fill="#202325"
                    fillOpacity="0.2"
                ></circle>
            </g>
            <defs>
                <clipPath id="clip0_3346_49931">
                    <rect width="40" height="40" x="0.32" fill="#fff" rx="20"></rect>
                </clipPath>
            </defs>
        </svg>
    )
}

export const SpacesIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="27"
            height="26"
            fill="none"
            viewBox="0 0 27 26"
        >
            <g
                stroke="#646464"
                strokeLinecap="round"
                strokeMiterlimit="10"
                clipPath="url(#clip0_3346_49950)"
            >
                <path d="M.741 8.549h8.39V8.8a4.196 4.196 0 0 1-4.195 4.194A4.196 4.196 0 0 1 .741 8.801z"></path>
                <path d="M9.13 8.549h8.39V8.8a4.196 4.196 0 0 1-4.195 4.194A4.196 4.196 0 0 1 9.13 8.801z"></path>
                <path d="M17.51 8.549h8.39V8.8a4.196 4.196 0 0 1-4.194 4.194 4.196 4.196 0 0 1-4.195-4.194zM2.929 25.58V12.544M6.94 25.58V12.544M11.313 25.58V12.544M15.331 25.58V12.544M19.701 25.58V12.544M23.712 25.58V12.544M.741 25.58h25.161M.741 8.548 11.908.855a2.49 2.49 0 0 1 2.827 0l11.167 7.693"></path>
            </g>
            <defs>
                <clipPath id="clip0_3346_49950">
                    <path fill="#fff" d="M.32 0h26v26h-26z"></path>
                </clipPath>
            </defs>
        </svg>
    )
}

export const AdventureIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="25"
            height="26"
            fill="none"
            viewBox="0 0 25 26"
        >
            <g stroke="#646464" strokeMiterlimit="10" clipPath="url(#clip0_3346_49965)">
                <path d="M23.294 18.225v2.706c0 2.56-2.063 4.638-4.605 4.638h-7.233a.28.28 0 0 1-.265-.362l.368-1.241a.5.5 0 0 1 .47-.354h1.601M23.292 15.07v-3.768c0-4.836-3.92-7.767-4.083-7.888l-.54.62"></path>
                <path d="M15.042 23.613h3.698a2.63 2.63 0 0 0 2.619-2.638v-2.75M21.36 15.069v-3.767c0-3.828-3.252-6.285-3.287-6.31 0 0-2.234-1.656-3.098-3.897-.188-.475.437-.88.83-.544l1.686 1.406M15.899 20.086c0 .862-.839 1.56-1.883 1.56s-1.883-.698-1.883-1.56M12.133 20.086h.796M15.103 20.086h.796M15.042 21.406v2.207M12.986 21.406v2.207"></path>
                <path d="M23.662 15.07h-2.67a.23.23 0 0 0-.231.233v2.69c0 .**************.233h2.671a.23.23 0 0 0 .231-.233v-2.69a.23.23 0 0 0-.23-.233ZM16.616 10.026c-1.19-.491-3.415-1.051-7.352-1.051s-6.163.56-7.352 1.051a1.9 1.9 0 0 0-1.156 1.957c.18 1.836.908 5.043 3.732 5.043 0 0 3.133.242 3.39-3.327.034-.518.23-1.009.607-1.362.206-.199.471-.345.78-.345.307 0 .564.146.778.345.377.353.565.853.608 1.362.256 3.56 3.39 3.327 3.39 3.327 2.832 0 3.551-3.207 3.73-5.043a1.9 1.9 0 0 0-1.155-1.957Z"></path>
                <path d="M11.182 15.672s-.333.93-1.6 1.38a.97.97 0 0 1-.625 0c-1.267-.45-1.6-1.38-1.6-1.38M16.618 10.026l.189-.284c.505-.759.12-1.794-.754-2.018-4.048-1.051-9.517-1.051-13.566 0-.873.224-1.258 1.268-.753 2.018l.188.284"></path>
            </g>
            <defs>
                <clipPath id="clip0_3346_49965">
                    <path fill="#fff" d="M.32 0h24v26h-24z"></path>
                </clipPath>
            </defs>
        </svg>
    )
}

export const AttireIcon = () => {
    return (
        <svg width="28" height="27" viewBox="0 0 28 27" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M8.97721 3.60733L8.00841 1.96912C7.62599 1.32687 6.86965 1.03832 6.18129 1.28033L5.76488 1.41995C5.34847 1.55957 5.01704 1.87604 4.83858 2.2856L1.32031 10.2719L3.50435 11.454L5.86686 6.88375V13.9671H9.20666V4.45436C9.20666 4.1565 9.13018 3.86795 8.97721 3.61664V3.60733Z" stroke="#646464" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M9.20898 4.45482C9.20898 4.15696 9.28547 3.86841 9.43844 3.6171L10.4072 1.97889C10.7897 1.33664 11.546 1.04809 12.2344 1.2901L12.6508 1.42972C13.0672 1.56934 13.3986 1.88581 13.5771 2.29536L17.0953 10.2816L14.9113 11.4637L12.5488 6.89351V13.9769H9.20898" stroke="#646464" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M12.5478 13.9688V26.1343H10.1258L9.20796 20.9777L8.28166 26.1343H5.86816V13.9688" stroke="#646464" strokeMiterlimit="10" strokeLinecap="round" />
            <path d="M22.6717 11.7593H21.1505C20.1987 11.7593 19.3149 11.2195 18.822 10.3352C18.5585 9.86049 18.4141 9.32063 18.4141 8.76215V4.48047C18.4141 4.48047 23.9974 4.6387 23.9974 7.28218" stroke="#646464" strokeMiterlimit="10" strokeLinecap="round" />
            <path d="M22.6768 5.4485C24.3254 4.55493 26.9344 4.48047 26.9344 4.48047V8.76215C26.9344 9.04139 26.9004 9.31132 26.8324 9.58125C26.7644 9.84187 26.6624 10.1025 26.5265 10.3352C26.0251 11.2195 25.1412 11.7593 24.1979 11.7593H22.6768" stroke="#646464" strokeMiterlimit="10" strokeLinecap="round" />
            <path d="M18.4072 4.48119V1" stroke="#646464" strokeMiterlimit="10" strokeLinecap="round" />
            <path d="M26.9316 4.48119V1" stroke="#646464" strokeMiterlimit="10" strokeLinecap="round" />
            <path d="M25.7596 11.2207C25.7596 11.2207 28.2581 15.3535 26.9323 26.3183H18.4171C17.0914 15.3535 19.5899 11.2207 19.5899 11.2207" stroke="#646464" strokeMiterlimit="10" strokeLinecap="round" />
        </svg>
    )
}

export const ProductionIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="28"
            height="28"
            fill="none"
            viewBox="0 0 28 28"
        >
            <path
                stroke="#646464"
                strokeLinecap="round"
                strokeMiterlimit="10"
                d="m14.284 7.807 8.128 9.045c1.47 1.627 1.536 3.981.157 5.36s-3.734 1.314-5.361-.157l-9.045-8.127"
            ></path>
            <path
                stroke="#646464"
                strokeLinecap="round"
                strokeMiterlimit="10"
                d="M7.788 13.936A6.468 6.468 0 1 0 7.788 1a6.468 6.468 0 0 0 0 12.936ZM22.522 22.213l4.799 4.8M18.886 18.595a1.18 1.18 0 0 1-1.66 0l-1.883-1.883a1.18 1.18 0 0 1 0-1.66 1.18 1.18 0 0 1 1.66 0l1.883 1.883a1.18 1.18 0 0 1 0 1.66Z"
            ></path>
        </svg>
    )
}

export const ToolsIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="26"
            height="25"
            fill="none"
            viewBox="0 0 26 25"
        >
            <g clipPath="url(#clip0_3346_49996)">
                <path
                    fill="#545454"
                    d="m18.215 17.112 4.564 4.564a.56.56 0 0 1 .165.392c0 .149-.055.29-.165.392a.54.54 0 0 1-.392.164.53.53 0 0 1-.392-.164l-4.564-4.564zm0-1.106-1.89 1.89 5.113 5.113a1.348 1.348 0 0 0 1.898 0 1.33 1.33 0 0 0 0-1.89l-5.113-5.113z"
                ></path>
                <path
                    stroke="#545454"
                    strokeLinecap="round"
                    strokeMiterlimit="10"
                    d="M11.196 2.189A6.15 6.15 0 0 0 5.769.487l3.05 3.05-1.05 3.914L3.856 8.5l-3.05-3.05A6.144 6.144 0 0 0 9 12.289l11.583 11.583a2.54 2.54 0 0 0 3.6 0 2.54 2.54 0 0 0 0-3.6L12.6 8.69a6.14 6.14 0 0 0-1.412-6.493z"
                ></path>
                <path
                    stroke="#545454"
                    strokeLinecap="round"
                    strokeMiterlimit="10"
                    d="m2.97 5.042.51-1.905 1.906-.51 1.388 1.396-.51 1.898-1.897.51zM3.44 21.66l-2.305 2.094s1.372.855 3.733.855 3.732-.855 3.732-.855L6.295 21.66M3.872 16.453h2.007c.235 0 .424.188.424.424v4.783H3.448v-4.783c0-.236.188-.424.424-.424ZM4.436 18.287l.87-.517M4.436 20.344l.87-.51M9.001 12.281l1.49-1.49"
                ></path>
            </g>
            <defs>
                <clipPath id="clip0_3346_49996">
                    <path fill="#fff" d="M.32 0h25v25h-25z"></path>
                </clipPath>
            </defs>
        </svg>
    )
}

export const SportsIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="25"
            height="23"
            fill="none"
            viewBox="0 0 25 23"
        >
            <g
                stroke="#545454"
                strokeLinecap="round"
                strokeMiterlimit="10"
                clipPath="url(#clip0_3346_50006)"
            >
                <path d="m21.41 8.707-.19.075-8.822 3.595-.025.006-.329-.131-8.84-3.545"></path>
                <path d="M3.203 12.14v7.674l9.157 2.86.013-.006 9.037-2.854v-7.498M12.374 12.385v10.289"></path>
                <path d="M7.372 7.22 3.204 8.709.864 11.28l9.271 3.407 1.917-2.435M17.372 7.234l4.072 1.456 2.334 2.572-2.365.866-6.906 2.535-1.872-2.378M12.633 2.99l.658 2.002c.************.272.2h2.119c.278 0 .398.358.17.521L14.14 6.95a.29.29 0 0 0-.108.32l.658 2.002c.088.263-.222.483-.443.32l-1.714-1.236a.29.29 0 0 0-.341 0L10.477 9.59c-.228.163-.531-.057-.443-.32l.658-2.002a.28.28 0 0 0-.108-.32L8.87 5.713c-.227-.163-.107-.52.171-.52h2.119a.28.28 0 0 0 .272-.201l.657-2.001a.29.29 0 0 1 .55 0zM12.356 1.21V.313M9.18 3.085l-.638-.634M15.543 3.061l.639-.633"></path>
            </g>
            <defs>
                <clipPath id="clip0_3346_50006">
                    <path fill="#fff" d="M.32 0h24v23h-24z"></path>
                </clipPath>
            </defs>
        </svg>
    )
}

export const FilterIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="14"
            fill="none"
            viewBox="0 0 16 14"
        >
            <path
                stroke="#1E1E1E"
                strokeLinecap="square"
                d="M4.32 2.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Zm0 0h10.5m-3 4.5a1.5 1.5 0 1 0 3 0 1.5 1.5 0 0 0-3 0Zm0 0H1.32m7.5 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0h-4.5"
            ></path>
        </svg>
    )
}

export const SearchIcon2 = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="32"
            height="31"
            fill="none"
            viewBox="0 0 32 31"
        >
            <circle cx="14.529" cy="14.208" r="7.75" stroke="#222"></circle>
            <path
                stroke="#2A4157"
                strokeLinecap="round"
                strokeWidth="0.5"
                d="M14.53 10.333a3.874 3.874 0 0 0-3.876 3.875"
            ></path>
            <path
                stroke="#222"
                strokeLinecap="round"
                d="m26.154 25.833-3.875-3.875"
            ></path>
        </svg>
    )
}

export const GoogleLoginIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="33"
            height="32"
            fill="none"
            viewBox="0 0 33 32"
        >
            <rect width="32" height="32" fill="#FDCA12" rx="5"></rect>
            <path fill="#3AB400" d="M0 14h18v18H5a5 5 0 0 1-5-5z"></path>
            <path fill="#3B95E8" d="M15 0h13a5 5 0 0 1 5 5v13H15z"></path>
        </svg>
    )
}

export const FacebookIcon2 = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="32"
            height="32"
            fill="none"
            viewBox="0 0 32 32"
        >
            <path
                fill="#fff"
                stroke="#fff"
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M24 2.667h-4a6.667 6.667 0 0 0-6.666 6.667v4h-4v5.333h4v10.667h5.333V18.667h4l1.334-5.333h-5.334v-4A1.334 1.334 0 0 1 20.001 8h4z"
            ></path>
        </svg>
    )
}

export const MailIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="35"
            height="35"
            fill="none"
            viewBox="0 0 35 35"
        >
            <path
                fill="#fff"
                d="M5.833 29.166q-1.203 0-2.078-.838a2.9 2.9 0 0 1-.839-2.078V8.75q0-1.203.839-2.042.875-.875 2.078-.875h23.333q1.203 0 2.042.875.875.839.875 2.042v17.5q0 1.202-.875 2.078a2.78 2.78 0 0 1-2.042.838zm11.666-10.208 11.667-7.292V8.75L17.499 16.04 5.833 8.75v2.916z"
            ></path>
        </svg>
    )
}

export const AlertPolygon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="41"
            height="42"
            fill="none"
            viewBox="0 0 41 42"
        >
            <path
                fill="#767676"
                d="M19.629 1.546a1 1 0 0 1 1.742 0l16.042 28.463a1 1 0 0 1-.871 1.491H4.458a1 1 0 0 1-.871-1.491z"
            ></path>
            <path
                fill="#fff"
                d="M20.8 24.3c.8 0 1.46.66 1.46 1.48 0 .8-.66 1.46-1.46 1.46-.82 0-1.48-.66-1.48-1.46 0-.82.66-1.48 1.48-1.48m1.08-2.52c-.02.4-.32.7-.72.7h-.74c-.4 0-.7-.3-.72-.7l-.24-8.04c0-.4.32-.74.72-.74h1.22c.4 0 .72.34.72.74z"
            ></path>
        </svg>
    )
}

export const PhoneIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="32"
            height="32"
            fill="none"
            viewBox="0 0 32 32"
        >
            <path
                fill="#fff"
                stroke="#fff"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M29.333 22.56v4a2.666 2.666 0 0 1-2.907 2.667 26.4 26.4 0 0 1-11.506-4.093 26 26 0 0 1-8-8 26.4 26.4 0 0 1-4.094-11.56A2.667 2.667 0 0 1 5.48 2.667h4a2.67 2.67 0 0 1 2.666 2.293c.17 1.28.482 2.537.934 3.747a2.67 2.67 0 0 1-.6 2.813l-1.694 1.694a21.33 21.33 0 0 0 8 8l1.694-1.694a2.67 2.67 0 0 1 2.813-.6c1.21.452 2.467.765 3.747.934a2.667 2.667 0 0 1 2.293 2.707"
            ></path>
        </svg>
    )
}

export const SearchSmallIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="18"
            height="18"
            fill="none"
            viewBox="0 0 18 18"
        >
            <path
                fill="#1D1B20"
                fillOpacity="0.71"
                d="m14.7 15.75-4.725-4.725a4.571 4.571 0 0 1-2.85.975q-2.043 0-3.46-1.416Q2.25 9.17 2.25 7.125q0-2.043 1.416-3.46Q5.08 2.25 7.125 2.25t3.46 1.416Q12 5.08 12 7.125a4.57 4.57 0 0 1-.975 2.85L15.75 14.7zM7.125 10.5q1.406 0 2.39-.984.985-.985.985-2.391t-.984-2.39Q8.53 3.75 7.125 3.75t-2.39.984q-.985.986-.985 2.391t.984 2.39q.986.985 2.391.985"
            ></path>
        </svg>
    )
}

export const LocationPinIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="51"
            height="50"
            fill="none"
            viewBox="0 0 51 50"
        >
            <rect width="50.82" height="50" fill="#CFCFCF" rx="11"></rect>
            <path
                fill="#707070"
                d="M25 35a.93.93 0 0 1-.6-.2 1.2 1.2 0 0 1-.375-.525 14 14 0 0 0-1.2-2.625q-.7-1.225-1.975-2.875t-2.075-3.15Q18 24.125 18 22q0-2.925 2.025-4.95Q22.075 15 25 15t4.95 2.05Q32 19.075 32 22q0 2.275-.875 3.8-.85 1.5-1.975 2.975-1.35 1.8-2.05 3a14 14 0 0 0-1.125 2.5 1.13 1.13 0 0 1-.4.55A.98.98 0 0 1 25 35m0-3.575q.425-.85.95-1.675.55-.825 1.6-2.2 1.075-1.4 1.75-2.575.7-1.2.7-2.975 0-2.075-1.475-3.525Q27.075 17 25 17t-3.55 1.475Q20 19.925 20 22q0 1.775.675 2.975a27 27 0 0 0 1.775 2.575q1.05 1.375 1.575 2.2.55.825.975 1.675m0-6.925q1.05 0 1.775-.725T27.5 22t-.725-1.775T25 19.5t-1.775.725T22.5 22t.725 1.775T25 24.5"
            ></path>
        </svg>
    )
}

export const ProfileIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="250"
            height="250"
            fill="none"
            viewBox="0 0 250 250"
        >
            <g clipPath="url(#clip0_76_3538)">
                <rect width="250" height="250" fill="#D9D9D9" rx="125"></rect>
                <ellipse
                    cx="125.255"
                    cy="241.254"
                    fill="#fff"
                    rx="77.255"
                    ry="77.254"
                ></ellipse>
                <circle cx="124.748" cy="95.748" r="47.748" fill="#fff"></circle>
            </g>
            <defs>
                <clipPath id="clip0_76_3538">
                    <rect width="250" height="250" fill="#fff" rx="125"></rect>
                </clipPath>
            </defs>
        </svg>
    )
}

export const UploadCloudIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="35"
            height="35"
            fill="none"
            viewBox="0 0 35 35"
        >
            <g clipPath="url(#clip0_76_3552)">
                <path
                    stroke="#fff"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="3"
                    d="M23.334 23.334 17.5 17.5m0 0-5.833 5.834M17.5 17.5v13.125m12.235-3.806a7.292 7.292 0 0 0-3.485-13.694h-1.837A11.666 11.666 0 1 0 4.375 23.771"
                ></path>
            </g>
            <defs>
                <clipPath id="clip0_76_3552">
                    <path fill="#fff" d="M0 0h35v35H0z"></path>
                </clipPath>
            </defs>
        </svg>
    )
}

export const CropIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="32"
            height="32"
            fill="none"
            viewBox="0 0 32 32"
        >
            <g clipPath="url(#clip0_79_500)">
                <path
                    stroke="#1E1E1E"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="3"
                    d="m8.174 1.333-.174 20A2.667 2.667 0 0 0 10.667 24h20M1.334 8.173l20-.173A2.667 2.667 0 0 1 24 10.666v20"
                ></path>
            </g>
            <defs>
                <clipPath id="clip0_79_500">
                    <path fill="#fff" d="M0 0h32v32H0z"></path>
                </clipPath>
            </defs>
        </svg>
    )
}

export const EmailIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="28"
            height="28"
            fill="none"
            viewBox="0 0 28 28"
        >
            <path
                fill="#2B2B2B"
                d="M4.667 23.334a2.32 2.32 0 0 1-1.662-.671A2.32 2.32 0 0 1 2.334 21V7q0-.962.67-1.633.701-.7 1.663-.7h18.667q.963 0 1.633.7.7.671.7 1.633v14q0 .963-.7 1.663-.67.67-1.633.67zm9.334-8.167 9.333-5.833V7l-9.333 5.834L4.667 7v2.334z"
            ></path>
        </svg>
    )
}

export const AlertPolygonRed = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="41"
            height="42"
            fill="none"
            viewBox="0 0 41 42"
        >
            <path
                fill="#E94848"
                d="M19.629 1.546a1 1 0 0 1 1.742 0l16.042 28.463a1 1 0 0 1-.871 1.491H4.458a1 1 0 0 1-.871-1.491z"
            ></path>
            <path
                fill="#fff"
                d="M20.8 24.3c.8 0 1.46.66 1.46 1.48 0 .8-.66 1.46-1.46 1.46-.82 0-1.48-.66-1.48-1.46 0-.82.66-1.48 1.48-1.48m1.08-2.52c-.02.4-.32.7-.72.7h-.74c-.4 0-.7-.3-.72-.7l-.24-8.04c0-.4.32-.74.72-.74h1.22c.4 0 .72.34.72.74z"
            ></path>
        </svg>
    )
}

export const PhoneIcon2 = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="32"
            height="32"
            fill="none"
            viewBox="0 0 32 32"
        >
            <path
                fill="#2B2B2B"
                stroke="#2B2B2B"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M29.334 22.56v4a2.67 2.67 0 0 1-2.907 2.667 26.4 26.4 0 0 1-11.506-4.093 26 26 0 0 1-8-8 26.4 26.4 0 0 1-4.094-11.56 2.667 2.667 0 0 1 2.654-2.907h4a2.67 2.67 0 0 1 2.666 2.293c.17 1.28.482 2.537.934 3.747a2.67 2.67 0 0 1-.6 2.813l-1.694 1.694a21.34 21.34 0 0 0 8 8l1.694-1.694a2.67 2.67 0 0 1 2.813-.6c1.21.452 2.467.765 3.747.934a2.667 2.667 0 0 1 2.293 2.707"
            ></path>
        </svg>

    )
}