'use client'
import { useEffect, useState, useRef } from "react";
import { MapPin, Search as SearchIcon, Send, Clock, X, Search } from "lucide-react";
import { Loader } from "@googlemaps/js-api-loader";
import { ideaCardData } from "@/data";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import HeaderDropdown from "./HeaderDropdown";

const HeaderInner = ({ isInnerpages = false }: { isInnerpages?: boolean }) => {
    const router = useRouter();
    const searchParams = useSearchParams();

    // Header states based on user interaction
    const [headerState, setHeaderState] = useState < 'normal' | 'expanded' | 'location-focus' | 'location-typing' | 'query-focus' | 'query-typing' > ('normal');

    // Search functionality (reused from SearchBar)
    const [query, setQuery] = useState < string > ("");
    const [isQueryFocused, setIsQueryFocused] = useState < boolean > (false);
    const [isQueryHovered, setIsQueryHovered] = useState < boolean > (false);
    const [filteredQuerySuggestions, setFilteredQuerySuggestions] = useState < string[] > ([]);
    // Static suggestions from data
    const staticSuggestions = ideaCardData.map((item) => item.title);

    const [location, setLocation] = useState < string > ("");
    const [isLocationFocused, setIsLocationFocused] = useState < boolean > (false);
    const [isLocationHovered, setIsLocationHovered] = useState < boolean > (false);
    const [locationPredictions, setLocationPredictions] = useState < google.maps.places.AutocompletePrediction[] > ([]);
    const autocompleteServiceRef = useRef < google.maps.places.AutocompleteService | null > (null);

    const queryInputRef = useRef < HTMLInputElement | null > (null);
    const locationInputRef = useRef < HTMLInputElement | null > (null);
    const headerRef = useRef < HTMLDivElement | null > (null);

    // Persistent expanded flag – stays true until user clicks overlay or submits search
    const [keepExpanded, setKeepExpanded] = useState < boolean > (false);

    // Search history management
    const [searchHistory, setSearchHistory] = useState < { queries: string[], locations: string[] } > ({
        queries: [],
        locations: []
    });

    // Load search history from localStorage on mount
    useEffect(() => {
        const savedHistory = localStorage.getItem('youhook-search-history');
        if (savedHistory) {
            setSearchHistory(JSON.parse(savedHistory));
        }
    }, []);

    // Save to search history
    const saveToHistory = (query: string, location: string) => {
        const newHistory = {
            queries: [query, ...searchHistory.queries.filter(q => q !== query)].slice(0, 10),
            locations: [location, ...searchHistory.locations.filter(l => l !== location)].slice(0, 10)
        };
        setSearchHistory(newHistory);
        localStorage.setItem('youhook-search-history', JSON.stringify(newHistory));
    };

    // Load Google Maps API
    useEffect(() => {
        const loader = new Loader({
            apiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY ?? "",
            libraries: ["places"],
        });
        loader.load().then(() => {
            if (typeof window !== "undefined") {
                const win = window as typeof window & { google?: typeof google };
                if (win.google) {
                    autocompleteServiceRef.current = new google.maps.places.AutocompleteService();
                }
            }
        });
    }, []);

    // Filter query suggestions
    useEffect(() => {
        if (!query.trim()) {
            setFilteredQuerySuggestions([]);
            return;
        }
        const lower = query.toLowerCase();
        const allSuggestions = staticSuggestions;
        const filtered = allSuggestions.filter((title) => title.toLowerCase().includes(lower));
        setFilteredQuerySuggestions(filtered.slice(0, 6));
    }, [query]);

    // Fetch Google Places predictions
    useEffect(() => {
        if (!autocompleteServiceRef.current || !location.trim()) {
            setLocationPredictions([]);
            return;
        }
        const svc = autocompleteServiceRef.current;
        svc.getPlacePredictions({ input: location, types: ["(cities)"] }, (preds) => {
            setLocationPredictions(preds || []);
        });
    }, [location]);

    // Sync state with URL query params (for explore page)
    useEffect(() => {
        const urlQuery = searchParams.get('query') ?? '';
        const urlLocation = searchParams.get('location') ?? '';
        setQuery(urlQuery);
        setLocation(urlLocation);
    }, [searchParams]);

    // Update header state based on interactions
    useEffect(() => {
        if (!keepExpanded) {
            setHeaderState('normal');
            return;
        }
        if (isLocationFocused && location.trim() !== "") {
            setHeaderState('location-typing');
        } else if (isLocationFocused) {
            setHeaderState('location-focus');
        } else if (isQueryFocused && query.trim() !== "") {
            setHeaderState('query-typing');
        } else if (isQueryFocused) {
            setHeaderState('query-focus');
        } else {
            // Keep expanded when no inputs are focused but header should stay open
            setHeaderState('expanded');
        }
    }, [keepExpanded, isLocationFocused, isQueryFocused, location, query]);

    // No global click handler – overlay controls collapse

    // Highlight matching text in suggestions
    const renderHighlighted = (text: string, typed: string) => {
        const idx = text.toLowerCase().indexOf(typed.toLowerCase());
        if (idx === -1 || !typed) return <span className="text-brand-gray-1005">{text}</span>;
        const before = text.slice(0, idx);
        const match = text.slice(idx, idx + typed.length);
        const after = text.slice(idx + typed.length);
        return (
            <>
                {before && <span className="text-brand-gray-1005">{before}</span>}
                <span className="text-black">{match}</span>
                {after && <span className="text-brand-gray-1005">{after}</span>}
            </>
        );
    };

    // Form submit handler
    const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        if (!location.trim()) {
            locationInputRef.current?.focus();
            return;
        }
        if (!query.trim()) {
            queryInputRef.current?.focus();
            return;
        }
        // Save to history before navigation
        saveToHistory(query, location);

        router.push(`/explore?query=${encodeURIComponent(query)}&location=${encodeURIComponent(location)}`);
        // Collapse header
        setKeepExpanded(false);
        setIsQueryFocused(false);
        setIsLocationFocused(false);
        setHeaderState('normal');
    };

    // Suggestions container
    const SuggestionsContainer: React.FC<{ children: React.ReactNode }> = ({ children }) => (
        <div className="absolute left-0 right-0 top-full mt-3 py-10 bg-white rounded-2xl shadow-custom-4 z-50 max-h-[411px] overflow-auto no-scroll w-[120%]">
            {children}
        </div>
    );

    // Get current location
    const getCurrentLocation = () => {
        if (!navigator.geolocation) return;
        navigator.geolocation.getCurrentPosition((pos) => {
            const { latitude, longitude } = pos.coords;
            if (typeof google !== "undefined") {
                const geocoder = new google.maps.Geocoder();
                geocoder.geocode({ location: { lat: latitude, lng: longitude } }, (results) => {
                    if (results && results[0]) {
                        setLocation(results[0].formatted_address);
                        setIsLocationFocused(false);
                        setIsQueryFocused(true);
                        setTimeout(() => {
                            queryInputRef.current?.focus();
                        }, 0);
                    }
                });
            }
        });
    };

    return (
        <header className="border-b border-brand-gray-850">

            {/* Dynamic Header based on state */}
            {headerState === 'normal' && (
                <div className="bg-white fixed top-0 left-0 w-full z-50 px-6 py-4 flex items-center justify-between">
                    <Link href={"/"} className="text-2xl font-extrabold text-brand-blue-200/60 text-[42px]">
                        YouHook
                    </Link>
                    <div className="w-[500px]">

                        <div onClick={() => {
                            setKeepExpanded(true);
                            setHeaderState('expanded');
                            locationInputRef.current?.focus();
                        }} className="flex items-center w-full shadow-custom-5 hover:shadow-custom-6 rounded-full px-4 py-3 w-full cursor-pointer border border-brand-gray-850">
                            <div>
                                <Search className="text-black mr-6" size={20} />
                            </div>
                            <div>
                                <span className="text-brand-gray-550 ">{location || 'Philadelphia, PA'}</span>
                            </div>
                            <div className="w-[2px] h-[26px] bg-brand-gray-1009 ml-10 mr-6" />
                            <div>
                                <span className="text-brand-gray-550 ">{query || 'What are you looking for?'}</span>
                            </div>
                            <div className="ml-auto">
                                <div className="flex items-center justify-center bg-brand-blue-200/60 rounded-full w-[34px] h-[34px] ml-10">
                                    <Search className="text-white" size={20} />
                                </div>
                            </div>
                        </div>
                    </div>
                    <HeaderDropdown isInnerpages={isInnerpages} />
                </div>
            )}

            {keepExpanded && (
                <div ref={headerRef} className="bg-white fixed top-0 left-0 w-full z-50 px-6 flex justify-between border border-brand-gray-850 h-[160px] py-6">
                    <div className="w-[25%]">
                        <Link href={"/"} className="text-2xl font-extrabold text-brand-blue-200/60 text-[42px]">
                            YouHook
                        </Link>
                    </div>
                    <div className={`w-[50%] shadow-custom-5 rounded-full border border-brand-gray-850 self-end relative  transition-colors duration-200 ${isLocationFocused || isQueryFocused ? 'bg-brand-gray-600' : 'bg-white'
                        }`}>
                        <div className={`transition-opacity duration-200 ${isLocationFocused || isQueryFocused || isLocationHovered || isQueryHovered ? 'opacity-0' : 'opacity-100 w-px h-[40px] bg-brand-gray-980 absolute left-1/2 -translate-x-1/2 top-1/2 -translate-y-1/2 z-10'
                            }`} />
                        <form onSubmit={handleSubmit} className="flex items-center gap-4  mx-auto">
                            {/* Location Input */}
                            <div className="relative flex-1">
                                <div
                                    className={`flex items-center rounded-full transition-colors duration-200 px-4 py-3 ${isLocationFocused
                                        ? 'bg-white'
                                        : isQueryFocused
                                            ? 'bg-brand-gray-600'
                                            : isLocationHovered
                                                ? 'bg-brand-gray-600'
                                                : isQueryHovered
                                                    ? 'bg-white'
                                                    : 'bg-gray-50'
                                        }`}
                                    onMouseEnter={() => setIsLocationHovered(true)}
                                    onMouseLeave={() => setIsLocationHovered(false)}
                                >
                                    <div className="mr-6">
                                        <Search className="text-brand-black-200" size={20} />
                                    </div>
                                    <div>
                                        <label htmlFor="location" className="text-brand-black-200">Location</label>
                                        <input
                                            ref={locationInputRef}
                                            type="text"
                                            placeholder="Philadelphia, PA"
                                            value={location}
                                            onFocus={() => {
                                                setIsLocationFocused(true);
                                                setIsQueryFocused(false);
                                            }}
                                            onChange={(e) => setLocation(e.target.value)}
                                            className="outline-none bg-transparent w-full text-gray-700 placeholder-gray-400"
                                        />
                                        {location && (
                                            <X
                                                className="text-black cursor-pointer ml-2 absolute right-4 top-1/2 -translate-y-1/2 w-[22px] h-[22px]  hover:bg-brand-1500/50 rounded-full flex justify-center items-center p-1"
                                                size={16}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    setLocation("");
                                                    // maintain focus so header stays expanded
                                                    locationInputRef.current?.focus();
                                                }}
                                            />
                                        )}
                                    </div>
                                </div>

                                {/* Location suggestions */}
                                {isLocationFocused && (
                                    <SuggestionsContainer>
                                        {location.trim() === "" ? (
                                            <>
                                                {/* Current Location Option */}
                                                <div
                                                    className="flex items-center gap-x-4 p-3 rounded-xl mx-4 hover:bg-brand-gray-600 cursor-pointer"
                                                    onClick={getCurrentLocation}
                                                >
                                                    <span className="flex items-center w-[50px] h-[50px] bg-brand-gray-1006 rounded-xl justify-center">
                                                        <Send className="text-brand-gray-1005" size={20} />
                                                    </span>
                                                    Use Current Location
                                                </div>

                                                {/* Location History */}
                                                {searchHistory.locations.map((historyLocation, idx) => (
                                                    <div
                                                        key={idx}
                                                        className="flex items-center gap-x-4 p-3 rounded-xl mx-4 hover:bg-brand-gray-600 cursor-pointer"
                                                        onClick={() => {
                                                            setLocation(historyLocation);
                                                            setIsLocationFocused(false);
                                                            setIsQueryFocused(true);
                                                            setTimeout(() => {
                                                                queryInputRef.current?.focus();
                                                            }, 0);
                                                        }}
                                                    >
                                                        <span className="flex items-center w-[50px] h-[50px] bg-brand-gray-1006 rounded-xl justify-center">
                                                            <Clock className="text-brand-gray-1005" size={20} />
                                                        </span>
                                                        <span className="text-black">{historyLocation}</span>
                                                    </div>
                                                ))}
                                            </>
                                        ) : (
                                            <>
                                                {locationPredictions.length === 0 && (
                                                    <p className="px-4 py-3 text-gray-400 text-sm">Searching...</p>
                                                )}
                                                {locationPredictions.map((pred) => (
                                                    <div
                                                        key={pred.place_id}
                                                        className="flex items-center gap-x-4 p-3 rounded-xl mx-4 hover:bg-brand-gray-600 cursor-pointer"
                                                        onClick={() => {
                                                            setLocation(pred.description);
                                                            setIsLocationFocused(false);
                                                            setIsQueryFocused(true);
                                                            setTimeout(() => {
                                                                queryInputRef.current?.focus();
                                                            }, 0);
                                                        }}
                                                    >
                                                        <span className="flex items-center w-[50px] h-[50px] bg-brand-gray-1006 rounded-xl justify-center">
                                                            <MapPin className="text-brand-gray-1005" size={20} />
                                                        </span>
                                                        <span className="text-black">{pred.description}</span>
                                                    </div>
                                                ))}
                                            </>
                                        )}
                                    </SuggestionsContainer>
                                )}
                            </div>

                            {/* Query Input */}
                            <div className="relative flex-1">
                                <div
                                    className={`transition-colors duration-200 px-4 py-3 rounded-full ${isQueryFocused
                                        ? 'bg-white'
                                        : isLocationFocused
                                            ? 'bg-brand-gray-600'
                                            : isQueryHovered
                                                ? 'bg-brand-gray-600'
                                                : isLocationHovered
                                                    ? 'bg-white'
                                                    : 'bg-gray-50'
                                        }`}
                                    onMouseEnter={() => setIsQueryHovered(true)}
                                    onMouseLeave={() => setIsQueryHovered(false)}
                                >
                                    <div className="">
                                        <label htmlFor="location" className="text-brand-black-200">Search</label>
                                        <input
                                            ref={queryInputRef}
                                            type="text"
                                            placeholder="What are you looking for?"
                                            value={query}
                                            onChange={(e) => setQuery(e.target.value)}
                                            onFocus={() => {
                                                setIsQueryFocused(true);
                                                setIsLocationFocused(false);
                                            }}
                                            className="outline-none bg-transparent w-full text-gray-700 placeholder-gray-400"
                                        />
                                        {query && (
                                            <X
                                                className="text-black cursor-pointer ml-2 absolute right-4 top-1/2 -translate-y-1/2 w-[22px] h-[22px]  hover:bg-brand-1500/50 rounded-full flex justify-center items-center p-1"
                                                size={16}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    setQuery("");
                                                    queryInputRef.current?.focus();
                                                }}
                                            />
                                        )}
                                    </div>

                                    {/* Query suggestions */}
                                    {isQueryFocused && (query.trim() === "" || filteredQuerySuggestions.length > 0) && (
                                        <SuggestionsContainer>
                                            {query.trim() === "" ? (
                                                <>
                                                    {/* Query History */}
                                                    {searchHistory.queries.map((historyQuery, idx) => (
                                                        <div
                                                            key={idx}
                                                            className="hover:bg-brand-gray-600 p-3 rounded-xl mx-4 cursor-pointer flex items-center text-lg"
                                                            onClick={() => {
                                                                setQuery(historyQuery);
                                                                setIsQueryFocused(false);
                                                                setIsLocationFocused(true);
                                                                setTimeout(() => {
                                                                    locationInputRef.current?.focus();
                                                                }, 0);
                                                            }}
                                                        >
                                                            <span className="flex items-center w-[50px] h-[50px] bg-brand-gray-1006 rounded-xl justify-center mr-4">
                                                                <Clock className="text-brand-gray-1005" size={20} />
                                                            </span>
                                                            <span className="text-black">{historyQuery}</span>
                                                        </div>
                                                    ))}
                                                </>
                                            ) : (
                                                <>
                                                    {filteredQuerySuggestions.map((title, index) => (
                                                        <div
                                                            key={index}
                                                            className="hover:bg-brand-gray-600 p-3 rounded-xl mx-4 cursor-pointer flex items-center text-lg"
                                                            onClick={() => {
                                                                setQuery(title);
                                                                setIsQueryFocused(false);
                                                                setIsLocationFocused(true);
                                                                setTimeout(() => {
                                                                    locationInputRef.current?.focus();
                                                                }, 0);
                                                            }}
                                                        >
                                                            <span className="flex items-center w-[50px] h-[50px] bg-brand-gray-1006 rounded-xl justify-center mr-4">
                                                                <SearchIcon className="text-brand-gray-1005" size={20} />
                                                            </span>
                                                            {renderHighlighted(title, query)}
                                                        </div>
                                                    ))}
                                                </>
                                            )}
                                        </SuggestionsContainer>
                                    )}
                                </div>
                            </div>
                            <div className="pr-4">
                                <button type="submit" className={`cursor-pointer flex items-center justify-center bg-brand-blue-200/60 rounded-full h-[48px]  transition-all duration-200 ${isLocationFocused || isQueryFocused
                                    ? 'w-[107px] gap-x-2'
                                    : 'w-[48px]'
                                    }`}>
                                    <Search className="text-white" size={20} />
                                    {(isLocationFocused || isQueryFocused) && (
                                        <span className="text-white">Search</span>
                                    )}
                                </button>
                            </div>
                        </form>
                    </div>
                    <div className="w-[25%] flex items-center justify-end">
                        <HeaderDropdown isInnerpages={isInnerpages} />
                    </div>
                </div>
            )}

            {/* Page overlay when expanded – click to collapse */}
            {keepExpanded && (
                <div onClick={() => {
                    setKeepExpanded(false);
                    setIsQueryFocused(false);
                    setIsLocationFocused(false);
                    setHeaderState('normal');
                }} className="fixed top-0 left-0 w-full h-full bg-black/50 z-40" />
            )}

            {/* Content with appropriate top margin */}
            <div className={headerState === 'normal' ? 'mt-[120px]' : 'mt-[70px]'} />
        </header>
    );
};

export default HeaderInner;